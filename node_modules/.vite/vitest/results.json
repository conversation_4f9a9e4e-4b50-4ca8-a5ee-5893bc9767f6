{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 23.875125000000025, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 6.192083000000025, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.306291999999985, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 122.38054199999999, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 7.676583999999934, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.6211250000000064, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 9.160250000000019, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 26.248459000000025, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 7.792041999999981, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 30.436915999999997, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 3.1745839999999816, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.48391700000002, "failed": false}], [":tests/basic.test.ts", {"duration": 2.8488330000000133, "failed": false}]]}