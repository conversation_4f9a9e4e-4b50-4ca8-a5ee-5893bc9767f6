{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 22.661833, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 5.690334000000007, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.210249999999974, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 82.78145800000004, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 6.676249999999982, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.3139170000000604, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 8.921708000000024, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 24.669125000000008, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.409208000000035, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 29.176750000000027, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.9748749999999973, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.66666600000002, "failed": false}], [":tests/basic.test.ts", {"duration": 2.77800000000002, "failed": false}]]}