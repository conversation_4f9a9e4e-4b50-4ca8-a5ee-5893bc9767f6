/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var l=require("@lexical/utils"),m=require("lexical");let n=new Set(["http:","https:","mailto:","sms:","tel:"]);
class p extends m.ElementNode{static getType(){return"link"}static clone(a){return new p(a.__url,{rel:a.__rel,target:a.__target,title:a.__title},a.__key)}constructor(a,b={},e){super(e);let {target:h=null,rel:k=null,title:f=null}=b;this.__url=a;this.__target=h;this.__rel=k;this.__title=f}createDOM(a){let b=document.createElement("a");b.href=this.sanitizeUrl(this.__url);null!==this.__target&&(b.target=this.__target);null!==this.__rel&&(b.rel=this.__rel);null!==this.__title&&(b.title=this.__title);l.addClassNamesToElement(b,
a.theme.link);return b}updateDOM(a,b){let e=this.__url,h=this.__target,k=this.__rel,f=this.__title;e!==a.__url&&(b.href=e);h!==a.__target&&(h?b.target=h:b.removeAttribute("target"));k!==a.__rel&&(k?b.rel=k:b.removeAttribute("rel"));f!==a.__title&&(f?b.title=f:b.removeAttribute("title"));return!1}static importDOM(){return{a:()=>({conversion:q,priority:1})}}static importJSON(a){let b=r(a.url,{rel:a.rel,target:a.target,title:a.title});b.setFormat(a.format);b.setIndent(a.indent);b.setDirection(a.direction);
return b}sanitizeUrl(a){try{let b=new URL(a);if(!n.has(b.protocol))return"about:blank"}catch(b){}return a}exportJSON(){return{...super.exportJSON(),rel:this.getRel(),target:this.getTarget(),title:this.getTitle(),type:"link",url:this.getURL(),version:1}}getURL(){return this.getLatest().__url}setURL(a){this.getWritable().__url=a}getTarget(){return this.getLatest().__target}setTarget(a){this.getWritable().__target=a}getRel(){return this.getLatest().__rel}setRel(a){this.getWritable().__rel=a}getTitle(){return this.getLatest().__title}setTitle(a){this.getWritable().__title=
a}insertNewAfter(a,b=!0){a=r(this.__url,{rel:this.__rel,target:this.__target,title:this.__title});this.insertAfter(a,b);return a}canInsertTextBefore(){return!1}canInsertTextAfter(){return!1}canBeEmpty(){return!1}isInline(){return!0}extractWithChild(a,b){if(!m.$isRangeSelection(b))return!1;a=b.anchor.getNode();let e=b.focus.getNode();return this.isParentOf(a)&&this.isParentOf(e)&&0<b.getTextContent().length}}
function q(a){let b=null;if(l.isHTMLAnchorElement(a)){let e=a.textContent;if(null!==e&&""!==e||0<a.children.length)b=r(a.getAttribute("href")||"",{rel:a.getAttribute("rel"),target:a.getAttribute("target"),title:a.getAttribute("title")})}return{node:b}}function r(a,b){return m.$applyNodeReplacement(new p(a,b))}function u(a){return a instanceof p}
class v extends p{static getType(){return"autolink"}static clone(a){return new v(a.__url,{rel:a.__rel,target:a.__target,title:a.__title},a.__key)}static importJSON(a){let b=w(a.url,{rel:a.rel,target:a.target,title:a.title});b.setFormat(a.format);b.setIndent(a.indent);b.setDirection(a.direction);return b}static importDOM(){return null}exportJSON(){return{...super.exportJSON(),type:"autolink",version:1}}insertNewAfter(a,b=!0){a=this.getParentOrThrow().insertNewAfter(a,b);return m.$isElementNode(a)?
(b=w(this.__url,{rel:this.__rel,target:this.__target,title:this.__title}),a.append(b),b):null}}function w(a,b){return m.$applyNodeReplacement(new v(a,b))}let x=m.createCommand("TOGGLE_LINK_COMMAND");function y(a,b){for(;null!==a&&null!==a.getParent()&&!b(a);)a=a.getParentOrThrow();return b(a)?a:null}exports.$createAutoLinkNode=w;exports.$createLinkNode=r;exports.$isAutoLinkNode=function(a){return a instanceof v};exports.$isLinkNode=u;exports.AutoLinkNode=v;exports.LinkNode=p;
exports.TOGGLE_LINK_COMMAND=x;
exports.toggleLink=function(a,b={}){let {target:e,title:h}=b,k=void 0===b.rel?"noreferrer":b.rel;b=m.$getSelection();if(m.$isRangeSelection(b))if(b=b.extract(),null===a)b.forEach(f=>{f=f.getParent();if(u(f)){let d=f.getChildren();for(let c=0;c<d.length;c++)f.insertBefore(d[c]);f.remove()}});else{if(1===b.length){let c=y(b[0],u);if(null!==c){c.setURL(a);void 0!==e&&c.setTarget(e);null!==k&&c.setRel(k);void 0!==h&&c.setTitle(h);return}}let f=null,d=null;b.forEach(c=>{var g=c.getParent();if(g!==d&&null!==
g&&(!m.$isElementNode(c)||c.isInline()))if(u(g))d=g,g.setURL(a),void 0!==e&&g.setTarget(e),null!==k&&d.setRel(k),void 0!==h&&d.setTitle(h);else if(g.is(f)||(f=g,d=r(a,{rel:k,target:e,title:h}),u(g)?null===c.getPreviousSibling()?g.insertBefore(d):g.insertAfter(d):c.insertBefore(d)),u(c)){if(!c.is(d)){if(null!==d){g=c.getChildren();for(let t=0;t<g.length;t++)d.append(g[t])}c.remove()}}else null!==d&&d.append(c)})}}
