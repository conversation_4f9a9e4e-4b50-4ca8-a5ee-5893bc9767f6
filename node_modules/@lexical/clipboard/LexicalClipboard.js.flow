/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {
  BaseSelection,
  LexicalEditor,
  RangeSelection,
  LexicalNode,
} from 'lexical';

/*
 * Rich Text
 */

declare export function $insertDataTransferForRichText(
  dataTransfer: DataTransfer,
  selection: BaseSelection,
  editor: LexicalEditor,
): void;

declare export function $getHtmlContent(editor: LexicalEditor): string | null;
declare export function $getLexicalContent(
  editor: LexicalEditor,
): string | null;

declare export function $insertGeneratedNodes(
  editor: LexicalEditor,
  nodes: Array<LexicalNode>,
  selection: BaseSelection,
): void;
/*
 * Plain Text
 */

/*
 * Export as JSON
 */

declare export function $insertDataTransferForPlainText(
  dataTransfer: DataTransfer,
  selection: RangeSelection,
): void;

interface BaseSerializedNode {
  children?: Array<BaseSerializedNode>;
  type: string;
  version: number;
}

declare export function $generateJSONFromSelectedNodes<
  SerializedNode: BaseSerializedNode,
>(
  editor: LexicalEdit<PERSON>,
  selection: BaseSelection | null,
): {
  namespace: string,
  nodes: Array<SerializedNode>,
};

declare export function $generateNodesFromSerializedNodes(
  serializedNodes: Array<BaseSerializedNode>,
): Array<LexicalNode>;

declare export function copyToClipboard(
  editor: LexicalEditor,
  event: null | ClipboardEvent,
): Promise<boolean>;
